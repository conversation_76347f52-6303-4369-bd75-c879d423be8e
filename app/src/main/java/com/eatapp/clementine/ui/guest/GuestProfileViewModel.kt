package com.eatapp.clementine.ui.guest

import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.eatapp.clementine.GuestVouchersHandler
import com.eatapp.clementine.VoucherUpdateState
import com.eatapp.clementine.VouchersHandler
import com.eatapp.clementine.data.network.response.custom_fields.CustomField
import com.eatapp.clementine.data.network.response.custom_fields.CustomFieldAttributes
import com.eatapp.clementine.data.network.response.custom_fields.CustomFieldComponent
import com.eatapp.clementine.data.network.response.guest.Guest
import com.eatapp.clementine.data.network.response.restaurant.AccountStateType
import com.eatapp.clementine.data.network.response.tag.Tag
import com.eatapp.clementine.data.network.response.tagging.Tagging
import com.eatapp.clementine.data.network.response.tagging.TaggingAttributes
import com.eatapp.clementine.data.network.response.user.Permission
import com.eatapp.clementine.data.network.response.vouchers.Voucher
import com.eatapp.clementine.data.network.response.vouchers.VoucherAssignmentModel
import com.eatapp.clementine.data.network.response.vouchers.VoucherType
import com.eatapp.clementine.data.repository.CustomFieldsRepository
import com.eatapp.clementine.data.repository.GuestsRepository
import com.eatapp.clementine.data.repository.TagRepository
import com.eatapp.clementine.data.repository.VoucherRepository
import com.eatapp.clementine.internal.EatException
import com.eatapp.clementine.internal.ItemsGroup
import com.eatapp.clementine.internal.SelectorItem
import com.eatapp.clementine.internal.SingleLiveEvent
import com.eatapp.clementine.internal.customFieldType
import com.eatapp.clementine.internal.labelized
import com.eatapp.clementine.internal.managers.AnalyticsManager
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.internal.managers.TagType
import com.eatapp.clementine.internal.managers.manageGuests
import com.eatapp.clementine.internal.managers.manageReservations
import com.eatapp.clementine.ui.base.BaseViewModel
import com.eatapp.clementine.ui.reservation.ReservationDetailsViewModel
import com.eatapp.clementine.views.StepperView
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import org.json.JSONObject
import java.util.Calendar
import java.util.Date
import javax.inject.Inject

@HiltViewModel
class GuestProfileViewModel @Inject constructor(
    private val eatManager: EatManager,
    val analyticsManager: AnalyticsManager,
    private val guestsRepository: GuestsRepository,
    @GuestVouchersHandler
    private val vouchersHandler: VouchersHandler,
    tagRepository: TagRepository,
    private val customFieldsRepository: CustomFieldsRepository,
    voucherRepository: VoucherRepository
) : BaseViewModel() {

    enum class GuestStatus {
        None,
        Updated,
        Added,
        Deleted
    }

    val isFreemium = eatManager.accountState() == AccountStateType.IN_HOUSE
    val isMarketingVisible = eatManager.restaurant()?.marketingOptInVisibility ?: false

    var currency: String? = eatManager.restaurant()?.currency ?: "AED"
    val posActive: Boolean = eatManager.restaurant()?.posActive == true
    val loyaltyActive: Boolean = eatManager.restaurant()?.loyaltyEnabled == true

    var permissionReservation: Permission = eatManager.permission(identifier = manageReservations)

    val permissionGuest = MediatorLiveData(eatManager.permission(identifier = manageGuests))

    var guest = MutableLiveData<Guest>()
    var guestStatus = GuestStatus.None

    var tagsAvailable: Boolean = true

    var update = SingleLiveEvent<Boolean>()
    var create = SingleLiveEvent<Boolean>()
    var deleteCompleted = SingleLiveEvent<Boolean>()
    var deleteInProgress = SingleLiveEvent<Boolean>()

    private val _voucherState =
        MutableStateFlow<VoucherUpdateState<Guest>>(VoucherUpdateState.Idle)
    val voucherState: StateFlow<VoucherUpdateState<Guest>> = _voucherState

    var call = MutableLiveData<String>()

    var simplifiedMode = MutableLiveData<Boolean>()
    var createMode = MutableLiveData<Boolean>()

    private var taggings: MutableList<Tagging>? = mutableListOf()

    private var reservationId: String? = null

    val tags: LiveData<List<Tag>> = tagRepository.tags
    val vouchers: LiveData<List<Voucher>> = voucherRepository.vouchers
    val customFields: LiveData<List<CustomField>> = customFieldsRepository.customFields

    init {
        permissionGuest.addSource(guest) {
            permissionGuest.value = eatManager.permission(identifier = manageGuests)
        }
    }

    fun setGuest(g: Guest) {
        taggings = g.taggings
        guest.value = g
        loadLtv()
    }

    fun setCreateMode() {
        createMode.value = true
        val g = Guest()
        g.marketingAccepted = eatManager.restaurant()?.marketingOptInDefaultValue ?: false
        guest.value = g
    }

    fun onUpdateGuest() {

        if (validate()) return

        analyticsManager.trackEditGuestProfile(JSONObject())

        launch({

            update.postValue(true)

            val guest = guestsRepository.updateGuest(
                guest.value?.id!!,
                guest.value!!.toGuestBody(
                    eatManager.addedTags(taggings, guest.value?.taggings),
                    eatManager.removedTags(taggings, guest.value?.taggings),
                ),
                taggings
            )

            <EMAIL>(guest)
            guestStatus = GuestStatus.Updated

            update.postValue(false)

        }, false)
    }

    fun onCreateGuest() {

        if (validate()) return

        launch({

            create.postValue(true)

            val guest = guestsRepository.createGuest(
                guest.value!!.toGuestBody(
                    eatManager.addedTags(taggings, guest.value?.taggings),
                    eatManager.removedTags(taggings, guest.value?.taggings)
                )
            )

            <EMAIL>(guest)
            guestStatus = GuestStatus.Added

            create.postValue(false)

        }, false)
    }

    fun onDeleteGuest() {

        analyticsManager.trackDeleteGuestProfile()

        viewModelScope.launch {
            deleteInProgress.postValue(true)

            try {
                guestsRepository.deleteGuest(guest.value?.id!!)
                guestStatus = GuestStatus.Deleted
                deleteCompleted.postValue(true)
            } catch (e: Exception) {
                setError(EatException(e, false))
            } finally {
                deleteInProgress.postValue(false)
            }
        }
    }

    fun onPhoneGuest() {
        call.postValue(guest.value?.phone)
    }

    fun updateDate(date: GuestProfileFragment.Dates, year: Int, month: Int, day: Int) {

        val c = Calendar.getInstance()

        when (date) {
            GuestProfileFragment.Dates.BIRTHDAY -> c.time = guest.value?.birthday ?: Date()
            GuestProfileFragment.Dates.ANNIVERSARY -> c.time = guest.value?.anniversary ?: Date()
        }

        c.set(Calendar.YEAR, year)
        c.set(Calendar.MONTH, month)
        c.set(Calendar.DAY_OF_MONTH, day)

        when (date) {
            GuestProfileFragment.Dates.BIRTHDAY -> guest.value?.birthday = c.time
            GuestProfileFragment.Dates.ANNIVERSARY -> guest.value?.anniversary = c.time
        }
    }

    fun phoneNumber(s: String) {
        guest.value?.phone = s
    }

    fun firstName(s: String) {
        guest.value?.firstName = s
    }

    fun lastName(s: String) {
        guest.value?.lastName = s
    }

    fun email(email: String) {
        guest.value?.email = email
    }

    fun updateNote(note: String?) {
        guest.value?.notes = note
    }

    fun taggingsList(): MutableList<SelectorItem>? {

        return eatManager.taggingSelectors(
            addItem = tagsList().size > 0,
            taggings = taggings,
            tags.value
        )
    }

    fun tagsList(): MutableList<ItemsGroup> {
        return eatManager.tagSelectors(
            taggings = guest.value?.taggings,
            tagType = TagType.Guest,
            tags = tags.value
        )
    }

    fun removeTag(tag: SelectorItem) {

        val list = taggings?.toMutableList()

        list?.remove(
            taggings!!
                .find { it.name == tag.name })

        taggings = list
    }

    fun updateTags(list: MutableList<ItemsGroup>?) {

        taggings = mutableListOf()

        list?.forEach { group ->
            taggings?.addAll(group.items.filter { it.isSelected }.map { item ->
                val tag = item.value as Tag
                Tagging(
                    TaggingAttributes(
                        tag.category?.attributes?.name,
                        tag.attributes.icon,
                        tag.attributes.name,
                        ""
                    ),
                    tag.id,
                    "",
                    tag.category?.attributes?.color
                )
            }.toMutableList())
        }
    }

    fun customFields(): List<CustomField> {
        val customFields = customFieldsRepository.customFields.value?.filter {
            it.attributes.component == CustomFieldComponent.GUEST
        }?.toMutableList() ?: mutableListOf()

        customFields.forEach {
            it.value = guest.value?.attributes?.customFields?.get(it.attributes.name)
        }

        guest.value?.attributes?.customFields?.filterNot { customField ->
            customField.key in customFields.map { it.attributes.name }
        }?.let { deletedFields ->
            customFields.addAll(deletedFields.map {
                CustomField(
                    it.key,
                    CustomFieldAttributes(
                        CustomFieldComponent.GUEST,
                        it.key,
                        "",
                        it.key.labelized,
                        it.value.customFieldType,
                        it.value as? List<String>
                    ),
                    deleted = true,
                    it.value
                )
            })
        }

        return customFields
    }

    fun updateCustomFields(field: Pair<String, Any>) {
        guest.value?.let { guest ->
            when (val fieldValue = field.second) {
                is Boolean -> {
                    if (fieldValue) {
                        guest.attributes?.customFields?.set(field.first, fieldValue)
                    } else {
                        guest.attributes?.customFields?.remove(field.first)
                    }
                }

                is String -> {
                    if (fieldValue.isNotEmpty()) {
                        guest.attributes?.customFields?.set(field.first, fieldValue)
                    } else {
                        guest.attributes?.customFields?.remove(field.first)
                    }
                }

                is Int -> {
                    updateCustomFieldCount(fieldValue, field.first)
                }

                is ArrayList<*> -> {
                    updateCustomFieldMulti(fieldValue as ArrayList<String>, field.first)
                }

                else -> {}
            }

            <EMAIL>(guest)
        }
    }

    fun updateCustomFields(customFieldName: String, stepper: StepperView.Step) {
        guest.value?.let { guest ->

            var count = guest.attributes?.customFields?.get(customFieldName) as? Int ?: 0
            when (stepper) {
                StepperView.Step.PLUS -> count++
                StepperView.Step.MINUS -> count--
            }

            if (count < 0 || count > ReservationDetailsViewModel.MAX_COVERS) return

            updateCustomFieldCount(count, customFieldName)

            <EMAIL>(guest)
        }
    }

    fun vouchers(): List<Voucher> {
        return vouchers.value ?: emptyList()
    }

    fun vouchersEnabled(): Boolean {
        return eatManager.restaurant()?.vouchersEnabled == true
    }

    fun guestVouchers(): List<Voucher> {
        return vouchers.value?.filter { voucher ->
            val context = voucher.attributes.context ?: emptyList()
            context.contains(VoucherType.GUEST)
        } ?: emptyList()
    }

    fun updateVoucherAssignments(vouchers: List<Voucher>) {
        viewModelScope.launch {
            vouchersHandler
                .updateVoucherAssignmentsFlow<Guest>(
                    guest.value!!.id,
                    editedBy = null,
                    vouchers
                )
                .collect {
                    handleVoucherResponse(it)
                    _voucherState.value = it
                }
        }
    }

    fun redeemVoucher(assignment: VoucherAssignmentModel) {
        viewModelScope.launch {
            vouchersHandler.redeemVoucherFlow<Guest>(
                guest.value!!.id,
                reservationId,
                editedBy = null,
                assignment
            ).collect {
                handleVoucherResponse(it)
                _voucherState.value = it
            }
        }
    }

    private fun handleVoucherResponse(it: VoucherUpdateState<Guest>) {
        if (it is VoucherUpdateState.Error) {
            setError(it.exception)
        } else if (it is VoucherUpdateState.Success) {
            setGuest(it.data)
        }
    }

    fun updateReservationId(reservationId: String?) {
        this.reservationId = reservationId
    }

    fun counterList(customFieldName: String): MutableList<SelectorItem>? {
        val times = mutableListOf<SelectorItem>()

        for (i in 1..ReservationDetailsViewModel.MAX_COVERS) {

            val isSelected =
                i == guest.value?.attributes?.customFields?.get(customFieldName) as? Int

            val item = SelectorItem(
                "", i.toString(), i,
                isSelected = isSelected,
                isHeader = false,
                isDisabled = false
            )
            times.add(item)
        }

        return times
    }

    private fun updateCustomFieldCount(count: Int, customFieldName: String) {
        guest.value?.attributes?.customFields?.apply {
            if (count == 0) remove(customFieldName)
            else set(customFieldName, count)
        }
    }

    private fun updateCustomFieldMulti(values: ArrayList<String>, customFieldName: String) {
        guest.value?.attributes?.customFields?.apply {
            if (values.isEmpty()) remove(customFieldName)
            else set(customFieldName, values)
        }
    }

    private fun validate(): Boolean {

        return if (guest.value?.firstName.isNullOrBlank()) {

            setError(
                EatException("Validation", "First name can not be empty")
            )
            true

        } else false
    }

    private fun loadLtv() {
        launch({
            val guest = guestsRepository.guest(guest.value!!.id)
            taggings = guest.taggings
            <EMAIL> = guest
        })
    }
}
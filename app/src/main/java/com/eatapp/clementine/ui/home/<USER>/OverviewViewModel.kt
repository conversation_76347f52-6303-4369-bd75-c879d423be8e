package com.eatapp.clementine.ui.home.overview

import android.content.SharedPreferences
import android.util.Log
import androidx.core.content.edit
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.BuildConfig
import com.eatapp.clementine.data.network.response.daynote.DayNoteData
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.user.Permission
import com.eatapp.clementine.data.repository.DayNoteRepository
import com.eatapp.clementine.data.repository.FabricateRepository
import com.eatapp.clementine.data.repository.ReservationsRepository
import com.eatapp.clementine.internal.asLiveData
import com.eatapp.clementine.internal.managers.AnalyticsManager
import com.eatapp.clementine.internal.managers.DAILY_NOTES_SEEN
import com.eatapp.clementine.internal.managers.DataManager
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.internal.managers.FeatureFlagsManager
import com.eatapp.clementine.internal.managers.PrintManager
import com.eatapp.clementine.internal.managers.manageReservations
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.first
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import javax.inject.Inject
import kotlin.concurrent.fixedRateTimer

@HiltViewModel
open class OverviewViewModel @Inject constructor(
    val eatManager: EatManager,
    val dataManager: DataManager,
    val analyticsManager: AnalyticsManager,
    val featureFlagsManager: FeatureFlagsManager,
    val printManager: PrintManager,
    private val reservationsRepository: ReservationsRepository,
    private val fabricateRepository: FabricateRepository,
    private val sharedPreferences: SharedPreferences,
    private val dayNoteRepository: DayNoteRepository
) : DatesViewModel() {

    var permission: Permission = eatManager.permission(identifier = manageReservations)

    private var jobActive: Boolean = false
    private var job: Job? = null

    private val _reservations = MutableLiveData<List<Reservation>>()
    val reservations: LiveData<List<Reservation>> by lazy {
        _reservations.asLiveData()
    }

    private val _showNps = MutableLiveData<String>()
    val showNps: LiveData<String> by lazy {
        _showNps.asLiveData()
    }

    private val _showOnboardingSetup = MutableLiveData<Boolean>()
    val showOnboardingSetup: LiveData<Boolean> by lazy {
        _showOnboardingSetup.asLiveData()
    }

    private val _dailyNotes = MutableLiveData<List<DayNoteData>>()
    val dailyNotes: LiveData<List<DayNoteData>> by lazy {
        _dailyNotes.asLiveData()
    }

    init {

        val isTestAccount = arrayOf(
            "@eatapp.co",
            "@example.com",
            "@test.com"
        ).any { eatManager.user()?.email?.contains(it) == true }

        if (!isTestAccount && featureFlagsManager.onboardingQuickSetupEnabled) {
            checkIfOnboardingCompleted()
        } else {
            surveys()
            customFields()
        }
    }

    fun initTimers() {

        stopTimers()

        if (!dataManager.isSubscribed() && !dataManager.isHydraEnabled()) {
            timers.add(fixedRateTimer("default", false, 5000L, 5000L) {
                reservations(true)
            })
        }

        timers.add(fixedRateTimer("default", false, 60000L, 60000L) {
            restaurant()
            surveys()
            customFields()
        })
    }

    private fun checkIfOnboardingCompleted() {

        launch({

            val response = fabricateRepository.pizzaSlicer()

            if (!response.pizzaSlicer.onboardingFlowComplete) {
                _showOnboardingSetup.postValue(true)
            } else {
                surveys()
            }
        })
    }

    fun updateDate() {

        job?.cancel()
        jobActive = false

        Log.v("current_date: ", date.value.toString())

        reservations(false)
    }

    fun markOnboardingAsDone() {
        _showOnboardingSetup.postValue(false)
    }

    fun markSurveyAsDone() {
        _showNps.postValue("")
    }

    fun stopTimers() {

        timers.forEach { it.cancel() }
        timers.clear()
    }

    fun reservations(poll: Boolean) {

        if (paused && poll) {
            stopTimers()
            return
        }

        job?.cancel()

        Log.v("reservations_api", "reservations ${date.value.toString()}")

        job = launch({
            reservationsRepository.reservations(date.value!!)
                .distinctUntilChanged()
                .collect {
                    _reservations.value = it
                }

//            /*
//             First delete all reservations for a current date,
//             in case any of them is deleted in the meanwhile
//             */
//            reservationsRepository.deleteReservations(eatManager.restaurantId(), date.value, endOfTheEatDay(date.value!!))
//
//            /*
//             Save the new set to database
//             */
//            reservationsRepository.saveReservations(response.reservations)
//
        }, poll)
    }

    fun dbReservations() {

//        launch({
//
//            val reservations =
//                reservationsRepository.reservations(date.value!!).firstOrNull()
//
//            if (!reservations.isNullOrEmpty()) {
//                _reservations.postValue(reservations!!)
//            }
//        })
    }

    fun restaurant() {

        launch({

            val response = fabricateRepository.restaurant(eatManager.restaurantId())
            eatManager.restaurant(response)

        }, true)
    }

    fun tags() {
        launch({
            fabricateRepository.tags(eatManager.restaurantId())
        }, true)
    }

    fun customFields() {
        launch({
            fabricateRepository.customFields(eatManager.restaurantId())
        }, true)
    }

    fun rooms() {

        launch({
            fabricateRepository.rooms(eatManager.restaurantId())
            tables()
        }, true)
    }

    fun tables() {
        launch({
            fabricateRepository.tables(eatManager.restaurantId())
        }, true)
    }

    fun messageTemplates(restaurantId: String = eatManager.restaurantId()): Job {
        return launch({
            fabricateRepository.messageTemplates(restaurantId)
        }, false)
    }

    fun whatsappTemplates(restaurantId: String = eatManager.restaurantId()): Job {

        return launch({
            fabricateRepository.whatsappTemplates(restaurantId)
        }, false)
    }

    fun vouchers(restaurantId: String = eatManager.restaurantId()): Job {
        return launch({
            fabricateRepository.vouchers(restaurantId)
        })
    }

    fun fetchDailyNotes() {
        launch({
            val dayNote = dayNoteRepository.dayNote(date.value!!).first()
            _dailyNotes.value = dayNote
        })
    }

    fun markDailyNotesSeen() {
        val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val today = sdf.format(Date())
        sharedPreferences.edit {
            putString(DAILY_NOTES_SEEN + eatManager.restaurantId(), today)
        }
    }

    fun shouldShowDailyNotes(): Boolean {
        val lastSeenDate =
            sharedPreferences.getString(DAILY_NOTES_SEEN + eatManager.restaurantId(), null)
        val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val today = sdf.format(Date())
        return lastSeenDate != today
    }

    fun offlineMode(): Boolean {
        return eatManager.offlineMode
    }

    fun toggleOfflineMode(offline: Boolean) {
        eatManager.offlineMode = offline
    }

    private fun surveys() {

        launch({

            val response = reservationsRepository.surveys()

            if (response.showSurvey && featureFlagsManager.npsEnabled) {
                configureNpsSurvey()
            }
        })
    }

    private fun configureNpsSurvey() {

        val subscriptionType = eatManager.productMessage()?.name?.type ?: "activetm"

        val url = BuildConfig.QUALTRICS_URL + "?restaurantId=${eatManager.restaurantId()}" +
                "&restaurantName=${eatManager.restaurant()?.name}&userName=${eatManager.userName()}&userEmail=${eatManager.userEmail()}" +
                "&userId=${eatManager.userId()}&subscriptionType=$subscriptionType&product=${BuildConfig.USER_AGENT} "

        _showNps.postValue(url)
    }
}

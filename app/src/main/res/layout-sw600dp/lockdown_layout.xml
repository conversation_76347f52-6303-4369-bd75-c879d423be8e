<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/lockdownView"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <View
            android:id="@+id/overlay"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginTop="?attr/actionBarSize"
            android:background="@color/transparent_10"
            android:clickable="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.cardview.widget.CardView
            android:id="@+id/alert_main"
            android:layout_width="650dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:alpha="0"
            app:cardCornerRadius="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:alpha="1">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <Button
                    android:id="@+id/main_alert_close"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_gravity="right"
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/ic_icon_cancel"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ScrollView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="76dp"
                    android:fillViewport="false">

                    <LinearLayout
                        android:id="@+id/linearLayout2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <ImageView
                            android:id="@+id/main_alert_graphic"
                            android:layout_width="215dp"
                            android:layout_height="215dp"
                            android:layout_gravity="center"
                            android:layout_marginTop="27dp"
                            app:srcCompat="@drawable/ic_graphic_lockdown"
                            tools:srcCompat="@drawable/ic_graphic_lockdown" />

                        <TextView
                            android:id="@+id/main_alert_title"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="30dp"
                            android:layout_marginTop="24dp"
                            android:layout_marginEnd="30dp"
                            android:fontFamily="@font/inter_semibold"
                            android:textAlignment="center"
                            android:textColor="@color/colorGrey800"
                            android:textSize="24sp"
                            tools:text="Your free trial has ended" />

                        <TextView
                            android:id="@+id/main_alert_text"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="34dp"
                            android:layout_marginTop="16dp"
                            android:layout_marginEnd="34dp"
                            android:fontFamily="@font/inter_regular"
                            android:textAlignment="center"
                            android:textColor="@color/colorDark50"
                            android:textSize="15sp"
                            tools:text="Don't worry, you'll still have access to all your current reservations - but you won’t be able to add new ones." />

                        <LinearLayout
                            android:id="@+id/main_alert_text_cont"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="9dp"
                            android:orientation="vertical"
                            android:paddingBottom="24dp"
                            app:layout_constraintTop_toBottomOf="@+id/main_alert_text" />

                    </LinearLayout>

                </ScrollView>

                <Button
                    android:id="@+id/main_alert_action"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/button_height"
                    android:layout_gravity="bottom"
                    android:layout_margin="20dp"
                    android:background="@drawable/shape_rounded_btn_bcg_green"
                    android:fontFamily="@font/inter_semibold"
                    android:textAllCaps="false"
                    android:textColor="@color/white"
                    android:textSize="15sp"
                    tools:text="Upgrade account now" />

            </FrameLayout>

        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:id="@+id/alert_small"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:layout_marginStart="@dimen/global_margin_16"
            android:layout_marginEnd="@dimen/global_margin_16"
            android:layout_marginBottom="@dimen/global_margin_16"
            android:alpha="0"
            android:visibility="visible"
            app:cardCornerRadius="8dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:alpha="1">

            <FrameLayout
                android:id="@+id/alert_small_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/colorRed50"
                android:orientation="vertical">

                <Button
                    android:id="@+id/small_alert_close"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_gravity="right"
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/ic_icon_cancel" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/small_alert_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/global_margin_16"
                        android:layout_marginTop="@dimen/global_margin_16"
                        android:layout_marginEnd="46dp"
                        android:fontFamily="@font/inter_bold"
                        android:text="TextView"
                        android:textColor="@color/colorDark100"
                        android:textSize="15sp" />

                    <TextView
                        android:id="@+id/small_alert_text"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/global_margin_16"
                        android:layout_marginTop="8dp"
                        android:layout_marginEnd="@dimen/global_margin_16"
                        android:layout_marginBottom="12dp"
                        android:fontFamily="@font/inter_medium"
                        android:text="TextView"
                        android:textColor="@color/colorDark100"
                        android:textSize="13sp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="right"
                        android:orientation="horizontal">

                        <Button
                            android:id="@+id/small_alert_action_secondary"
                            android:layout_width="wrap_content"
                            android:layout_height="32dp"
                            android:layout_marginBottom="12dp"
                            android:background="@null"
                            android:fontFamily="@font/inter_semibold"
                            android:paddingStart="@dimen/global_margin_16"
                            android:paddingEnd="@dimen/global_margin_16"
                            android:textAllCaps="false"
                            android:textColor="@color/colorPrimary"
                            android:textSize="13sp"
                            tools:text="Upgrade account" />

                        <Button
                            android:id="@+id/small_alert_action_primary"
                            android:layout_width="wrap_content"
                            android:layout_height="32dp"
                            android:layout_gravity="right"
                            android:layout_marginEnd="12dp"
                            android:layout_marginBottom="12dp"
                            android:background="@drawable/shape_rounded_btn_bcg_green"
                            android:fontFamily="@font/inter_semibold"
                            android:paddingStart="@dimen/global_margin_16"
                            android:paddingEnd="@dimen/global_margin_16"
                            android:textAllCaps="false"
                            android:textColor="@color/white"
                            android:textSize="13sp"
                            tools:text="Upgrade account now" />

                    </LinearLayout>


                </LinearLayout>

            </FrameLayout>

        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:id="@+id/banner_minimized"
            android:layout_width="56dp"
            android:layout_height="48dp"
            android:layout_gravity="bottom"
            android:layout_marginStart="-8dp"
            android:layout_marginTop="@dimen/global_margin_16"
            android:layout_marginBottom="80dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:alpha="0"
            android:visibility="visible"
            app:cardCornerRadius="8dp"
            tools:alpha="1">

            <ImageView
                android:id="@+id/banner_minimized_action"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/colorYellow50"
                android:padding="12dp"
                android:layout_marginStart="8dp"
                android:src="@drawable/ic_icon_cancel" />

        </androidx.cardview.widget.CardView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</merge>
